/**
 * Secure authentication utilities
 * Provides secure access to authentication tokens when needed for API calls
 */

import { debug, error } from './logger';

/**
 * Securely fetch access token from backend session
 * Only use this for API calls that specifically require an access token
 * @returns {Promise<string|null>} Access token or null if not available
 */
export async function getAccessToken() {
  try {
    const response = await fetch('/auth/access-token', {
      method: 'GET',
      credentials: 'include' // Include session cookies
    });

    if (response.ok) {
      const data = await response.json();
      debug('Access token retrieved securely');
      return data.access_token;
    } else if (response.status === 401) {
      debug('Not authenticated - no access token available');
      return null;
    } else {
      error('Failed to retrieve access token:', { status: response.status });
      return null;
    }
  } catch (err) {
    error('Error retrieving access token:', { error: err });
    return null;
  }
}

/**
 * Check if user is authenticated based on session
 * @returns {Promise<boolean>} True if authenticated, false otherwise
 */
export async function isAuthenticated() {
  try {
    const response = await fetch('/auth/userinfo', {
      method: 'GET',
      credentials: 'include'
    });

    if (response.ok) {
      const data = await response.json();
      return data.authenticated === true;
    }
    
    return false;
  } catch (err) {
    error('Error checking authentication status:', { error: err });
    return false;
  }
}

/**
 * Get current user information from session
 * @returns {Promise<object|null>} User info or null if not authenticated
 */
export async function getCurrentUser() {
  try {
    const response = await fetch('/auth/userinfo', {
      method: 'GET',
      credentials: 'include'
    });

    if (response.ok) {
      const data = await response.json();
      if (data.authenticated && data.user) {
        return data.user;
      }
    }
    
    return null;
  } catch (err) {
    error('Error getting current user:', { error: err });
    return null;
  }
}

/**
 * Clear local authentication state
 * Use this when logging out or when authentication becomes invalid
 */
export function clearLocalAuthState() {
  localStorage.removeItem('user_info');
  localStorage.removeItem('auth_status');
  debug('Local authentication state cleared');
}

/**
 * Set local authentication state
 * Use this after successful authentication
 * @param {object} userInfo - User information to store
 */
export function setLocalAuthState(userInfo) {
  localStorage.setItem('user_info', JSON.stringify(userInfo));
  localStorage.setItem('auth_status', 'authenticated');
  debug('Local authentication state set');
}
